#pragma once

#include "controllers/base_controller.hpp"
#include "handlers/rtsp_handler.hpp"
#include <memory>

namespace server::controllers {

/**
 * @brief Controller for RTSP stream management endpoints
 * Handles HTTP requests for RTSP stream operations
 */
class RTSPController : public BaseController {
public:
    RTSPController(std::shared_ptr<shared::ServerConfig> config,
                   std::shared_ptr<handlers::RTSPHandler> rtsp_handler);
    ~RTSPController() override = default;

    // HTTP endpoint handlers
    void handleAddStream(const httplib::Request& req, httplib::Response& res);
    void handleGetStreams(const httplib::Request& req, httplib::Response& res);
    void handleGetStream(const httplib::Request& req, httplib::Response& res);
    void handleUpdateStream(const httplib::Request& req, httplib::Response& res);
    void handleRemoveStream(const httplib::Request& req, httplib::Response& res);
    void handleGetStreamStats(const httplib::Request& req, httplib::Response& res);
    void handleControlStream(const httplib::Request& req, httplib::Response& res);

    // System endpoint handlers
    void handleGetSystemStatus(const httplib::Request& req, httplib::Response& res);
    void handleGetSystemStats(const httplib::Request& req, httplib::Response& res);

    // Route registration helper
    void registerRoutes(httplib::Server& server);

private:
    std::shared_ptr<handlers::RTSPHandler> rtsp_handler_;

    // HTTP processing helpers
    utils::JsonValue parseRequestBody(const httplib::Request& req, httplib::Response& res);
    void sendJsonResponse(httplib::Response& res, const utils::JsonValue& json_response);
    std::string extractStreamId(const httplib::Request& req);
    
    // Request validation
    bool validateJsonRequest(const httplib::Request& req, httplib::Response& res);
    bool validateStreamIdParam(const std::string& stream_id, httplib::Response& res);
};

} // namespace server::controllers
