#pragma once

#include "handlers/base_handler.hpp"
#include "rtsp/stream_multiplexer.hpp"
#include "rtsp/rtsp_config.hpp"
#include "rtsp/rtsp_types.hpp"
#include <memory>
#include <string>
#include <vector>

namespace server::handlers {

/**
 * @brief Handler for RTSP stream management operations
 * Handles business logic for RTSP stream operations
 */
class RTSPHandler : public BaseHandler {
public:
    RTSPHandler(std::shared_ptr<shared::ServerConfig> config,
                std::shared_ptr<aibox::rtsp::StreamMultiplexer> multiplexer);
    ~RTSPHandler() override = default;

    // Stream management operations
    utils::JsonValue handleAddStream(const utils::JsonValue& request);
    utils::JsonValue handleGetStreams();
    utils::JsonValue handleGetStream(const std::string& stream_id);
    utils::JsonValue handleUpdateStream(const std::string& stream_id, const utils::JsonValue& request);
    utils::JsonValue handleRemoveStream(const std::string& stream_id);
    utils::JsonValue handleGetStreamStats(const std::string& stream_id);
    utils::JsonValue handleControlStream(const std::string& stream_id, const utils::JsonValue& request);

    // System operations
    utils::JsonValue handleGetSystemStatus();
    utils::JsonValue handleGetSystemStats();

private:
    std::shared_ptr<aibox::rtsp::StreamMultiplexer> multiplexer_;

    // Helper methods
    aibox::rtsp::RTSPConnectionConfig parseStreamConfig(const utils::JsonValue& json);
    utils::JsonValue streamConfigToJson(const aibox::rtsp::RTSPConnectionConfig& config);
    utils::JsonValue streamInfoToJson(const aibox::rtsp::StreamInfo& info);
    utils::JsonValue streamStatsToJson(const aibox::rtsp::StreamStatistics& stats);
    utils::JsonValue systemHealthToJson(const aibox::rtsp::MultiplexerHealth& health);
    utils::JsonValue systemStatsToJson(const aibox::rtsp::MultiplexerStatistics& stats);

    // Validation helpers
    bool validateStreamId(const std::string& stream_id);
    bool validateStreamConfig(const aibox::rtsp::RTSPConnectionConfig& config);
    bool validateControlAction(const std::string& action);

    // Error handling
    utils::JsonValue createStreamNotFoundError(const std::string& stream_id);
    utils::JsonValue createInvalidConfigError(const std::string& details);
    utils::JsonValue createSystemError(const std::string& details);
};

} // namespace server::handlers
