#include <gtest/gtest.h>
#include <httplib.h>
#include <memory>
#include <thread>
#include <chrono>

#include "controllers/rtsp_controller.hpp"
#include "handlers/rtsp_handler.hpp"
#include "rtsp/stream_multiplexer.hpp"
#include "shared/config.hpp"
#include "utils/json_utils.hpp"

using namespace server;

class RTSPControllerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test configuration
        config_ = std::make_shared<shared::ServerConfig>();
        config_->host = "127.0.0.1";
        config_->port = 8081; // Use different port for testing
        config_->enable_cors = true;
        config_->enable_request_logging = false;

        // Create RTSP components
        stream_multiplexer_ = std::make_shared<aibox::rtsp::StreamMultiplexer>();
        rtsp_handler_ = std::make_shared<handlers::RTSPHandler>(config_, stream_multiplexer_);
        rtsp_controller_ = std::make_shared<controllers::RTSPController>(config_, rtsp_handler_);

        // Create and setup test server
        server_ = std::make_unique<httplib::Server>();
        rtsp_controller_->registerRoutes(*server_);

        // Start server in background thread
        server_thread_ = std::thread([this]() {
            server_->listen(config_->host, config_->port);
        });

        // Give server time to start
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    void TearDown() override {
        if (server_) {
            server_->stop();
        }
        if (server_thread_.joinable()) {
            server_thread_.join();
        }
    }

    std::shared_ptr<shared::ServerConfig> config_;
    std::shared_ptr<aibox::rtsp::StreamMultiplexer> stream_multiplexer_;
    std::shared_ptr<handlers::RTSPHandler> rtsp_handler_;
    std::shared_ptr<controllers::RTSPController> rtsp_controller_;
    std::unique_ptr<httplib::Server> server_;
    std::thread server_thread_;
};

TEST_F(RTSPControllerTest, GetStreamsEmpty) {
    httplib::Client client("127.0.0.1", 8081);
    
    auto res = client.Get("/api/v1/rtsp/streams");
    
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 200);
    EXPECT_EQ(res->get_header_value("Content-Type"), "application/json");
    
    // Parse response JSON
    auto json_result = utils::JsonUtils::parse(res->body);
    ASSERT_TRUE(json_result.has_value());
    
    auto json = json_result.value();
    ASSERT_TRUE(json.isObject());
    
    auto obj = json.asObject();
    EXPECT_TRUE(obj.find("success") != obj.end());
    EXPECT_TRUE(obj.at("success").asBool());
    
    EXPECT_TRUE(obj.find("data") != obj.end());
    auto data = obj.at("data").asObject();
    EXPECT_TRUE(data.find("total_count") != data.end());
    EXPECT_EQ(data.at("total_count").asNumber(), 0);
}

TEST_F(RTSPControllerTest, AddStreamValid) {
    httplib::Client client("127.0.0.1", 8081);
    
    // Create test stream configuration
    utils::JsonObject stream_config;
    stream_config["stream_id"] = utils::JsonValue("test_stream_1");
    stream_config["rtsp_url"] = utils::JsonValue("rtsp://admin:CMC2024!@192.168.222.169:554/streaming/channels/01");
    stream_config["username"] = utils::JsonValue("admin");
    stream_config["password"] = utils::JsonValue("CMC2024!");
    stream_config["enabled"] = utils::JsonValue(true);
    stream_config["priority"] = utils::JsonValue("high");
    stream_config["transport"] = utils::JsonValue("tcp");
    
    std::string json_body = utils::JsonUtils::stringify(utils::JsonValue(stream_config));
    
    auto res = client.Post("/api/v1/rtsp/streams", json_body, "application/json");
    
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 200);
    
    // Parse response JSON
    auto json_result = utils::JsonUtils::parse(res->body);
    ASSERT_TRUE(json_result.has_value());
    
    auto json = json_result.value();
    ASSERT_TRUE(json.isObject());
    
    auto obj = json.asObject();
    EXPECT_TRUE(obj.find("success") != obj.end());
    EXPECT_TRUE(obj.at("success").asBool());
}

TEST_F(RTSPControllerTest, AddStreamInvalidUrl) {
    httplib::Client client("127.0.0.1", 8081);
    
    // Create invalid stream configuration (missing rtsp:// prefix)
    utils::JsonObject stream_config;
    stream_config["stream_id"] = utils::JsonValue("test_stream_invalid");
    stream_config["rtsp_url"] = utils::JsonValue("http://invalid.url");
    
    std::string json_body = utils::JsonUtils::stringify(utils::JsonValue(stream_config));
    
    auto res = client.Post("/api/v1/rtsp/streams", json_body, "application/json");
    
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 400);
    
    // Parse response JSON
    auto json_result = utils::JsonUtils::parse(res->body);
    ASSERT_TRUE(json_result.has_value());
    
    auto json = json_result.value();
    ASSERT_TRUE(json.isObject());
    
    auto obj = json.asObject();
    EXPECT_TRUE(obj.find("success") != obj.end());
    EXPECT_FALSE(obj.at("success").asBool());
}

TEST_F(RTSPControllerTest, GetSystemStatus) {
    httplib::Client client("127.0.0.1", 8081);
    
    auto res = client.Get("/api/v1/rtsp/system/status");
    
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 200);
    EXPECT_EQ(res->get_header_value("Content-Type"), "application/json");
    
    // Parse response JSON
    auto json_result = utils::JsonUtils::parse(res->body);
    ASSERT_TRUE(json_result.has_value());
    
    auto json = json_result.value();
    ASSERT_TRUE(json.isObject());
    
    auto obj = json.asObject();
    EXPECT_TRUE(obj.find("success") != obj.end());
    EXPECT_TRUE(obj.at("success").asBool());
    
    EXPECT_TRUE(obj.find("data") != obj.end());
    auto data = obj.at("data").asObject();
    EXPECT_TRUE(data.find("status") != data.end());
}

TEST_F(RTSPControllerTest, GetSystemStats) {
    httplib::Client client("127.0.0.1", 8081);
    
    auto res = client.Get("/api/v1/rtsp/system/stats");
    
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 200);
    EXPECT_EQ(res->get_header_value("Content-Type"), "application/json");
    
    // Parse response JSON
    auto json_result = utils::JsonUtils::parse(res->body);
    ASSERT_TRUE(json_result.has_value());
    
    auto json = json_result.value();
    ASSERT_TRUE(json.isObject());
    
    auto obj = json.asObject();
    EXPECT_TRUE(obj.find("success") != obj.end());
    EXPECT_TRUE(obj.at("success").asBool());
}

TEST_F(RTSPControllerTest, GetNonExistentStream) {
    httplib::Client client("127.0.0.1", 8081);
    
    auto res = client.Get("/api/v1/rtsp/streams/nonexistent");
    
    ASSERT_TRUE(res);
    EXPECT_EQ(res->status, 404);
    
    // Parse response JSON
    auto json_result = utils::JsonUtils::parse(res->body);
    ASSERT_TRUE(json_result.has_value());
    
    auto json = json_result.value();
    ASSERT_TRUE(json.isObject());
    
    auto obj = json.asObject();
    EXPECT_TRUE(obj.find("success") != obj.end());
    EXPECT_FALSE(obj.at("success").asBool());
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
