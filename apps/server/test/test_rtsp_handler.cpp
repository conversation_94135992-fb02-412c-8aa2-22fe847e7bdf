#include <gtest/gtest.h>
#include <memory>

#include "handlers/rtsp_handler.hpp"
#include "rtsp/stream_multiplexer.hpp"
#include "shared/config.hpp"
#include "utils/json_utils.hpp"

using namespace server;

class RTSPHandlerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test configuration
        config_ = std::make_shared<shared::ServerConfig>();
        config_->host = "127.0.0.1";
        config_->port = 8081;
        config_->enable_cors = true;
        config_->enable_request_logging = false;

        // Create RTSP components
        stream_multiplexer_ = std::make_shared<aibox::rtsp::StreamMultiplexer>();
        rtsp_handler_ = std::make_shared<handlers::RTSPHandler>(config_, stream_multiplexer_);
    }

    void TearDown() override {
        // Cleanup
    }

    std::shared_ptr<shared::ServerConfig> config_;
    std::shared_ptr<aibox::rtsp::StreamMultiplexer> stream_multiplexer_;
    std::shared_ptr<handlers::RTSPHandler> rtsp_handler_;
};

TEST_F(RTSPHandlerTest, GetStreamsEmpty) {
    auto response = rtsp_handler_->handleGetStreams();
    
    ASSERT_TRUE(response.isObject());
    auto obj = response.asObject();
    
    EXPECT_TRUE(obj.find("success") != obj.end());
    EXPECT_TRUE(obj.at("success").asBool());
    
    EXPECT_TRUE(obj.find("data") != obj.end());
    auto data = obj.at("data").asObject();
    EXPECT_TRUE(data.find("total_count") != data.end());
    EXPECT_EQ(data.at("total_count").asNumber(), 0);
}

TEST_F(RTSPHandlerTest, AddStreamValid) {
    // Create test stream configuration
    utils::JsonObject stream_config;
    stream_config["stream_id"] = utils::JsonValue("test_stream_1");
    stream_config["rtsp_url"] = utils::JsonValue("rtsp://admin:CMC2024!@***************:554/streaming/channels/01");
    stream_config["username"] = utils::JsonValue("admin");
    stream_config["password"] = utils::JsonValue("CMC2024!");
    stream_config["enabled"] = utils::JsonValue(true);
    stream_config["priority"] = utils::JsonValue("high");
    stream_config["transport"] = utils::JsonValue("tcp");
    
    auto response = rtsp_handler_->handleAddStream(utils::JsonValue(stream_config));
    
    ASSERT_TRUE(response.isObject());
    auto obj = response.asObject();
    
    EXPECT_TRUE(obj.find("success") != obj.end());
    EXPECT_TRUE(obj.at("success").asBool());
}

TEST_F(RTSPHandlerTest, AddStreamInvalidUrl) {
    // Create invalid stream configuration (missing rtsp:// prefix)
    utils::JsonObject stream_config;
    stream_config["stream_id"] = utils::JsonValue("test_stream_invalid");
    stream_config["rtsp_url"] = utils::JsonValue("http://invalid.url");
    
    auto response = rtsp_handler_->handleAddStream(utils::JsonValue(stream_config));
    
    ASSERT_TRUE(response.isObject());
    auto obj = response.asObject();
    
    EXPECT_TRUE(obj.find("success") != obj.end());
    EXPECT_FALSE(obj.at("success").asBool());
}

TEST_F(RTSPHandlerTest, GetSystemStatus) {
    auto response = rtsp_handler_->handleGetSystemStatus();
    
    ASSERT_TRUE(response.isObject());
    auto obj = response.asObject();
    
    EXPECT_TRUE(obj.find("success") != obj.end());
    EXPECT_TRUE(obj.at("success").asBool());
    
    EXPECT_TRUE(obj.find("data") != obj.end());
    auto data = obj.at("data").asObject();
    EXPECT_TRUE(data.find("status") != data.end());
}

TEST_F(RTSPHandlerTest, GetSystemStats) {
    auto response = rtsp_handler_->handleGetSystemStats();
    
    ASSERT_TRUE(response.isObject());
    auto obj = response.asObject();
    
    EXPECT_TRUE(obj.find("success") != obj.end());
    EXPECT_TRUE(obj.at("success").asBool());
}

TEST_F(RTSPHandlerTest, GetNonExistentStream) {
    auto response = rtsp_handler_->handleGetStream("nonexistent");
    
    ASSERT_TRUE(response.isObject());
    auto obj = response.asObject();
    
    EXPECT_TRUE(obj.find("success") != obj.end());
    EXPECT_FALSE(obj.at("success").asBool());
}

TEST_F(RTSPHandlerTest, AddAndGetStream) {
    // First add a stream
    utils::JsonObject stream_config;
    stream_config["stream_id"] = utils::JsonValue("test_stream_2");
    stream_config["rtsp_url"] = utils::JsonValue("rtsp://admin:CMC2024!@***************:554/streaming/channels/01");
    stream_config["enabled"] = utils::JsonValue(true);
    
    auto add_response = rtsp_handler_->handleAddStream(utils::JsonValue(stream_config));
    ASSERT_TRUE(add_response.isObject());
    auto add_obj = add_response.asObject();
    EXPECT_TRUE(add_obj.at("success").asBool());
    
    // Then get the stream
    auto get_response = rtsp_handler_->handleGetStream("test_stream_2");
    ASSERT_TRUE(get_response.isObject());
    auto get_obj = get_response.asObject();
    EXPECT_TRUE(get_obj.at("success").asBool());
    
    // Verify stream data
    EXPECT_TRUE(get_obj.find("data") != get_obj.end());
    auto data = get_obj.at("data").asObject();
    EXPECT_TRUE(data.find("stream_id") != data.end());
    EXPECT_EQ(data.at("stream_id").asString(), "test_stream_2");
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
