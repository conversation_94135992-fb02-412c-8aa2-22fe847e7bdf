#include "controllers/rtsp_controller.hpp"
#include "shared/config.hpp"
#include <iostream>
#include <chrono>

namespace server::controllers {

RTSPController::RTSPController(std::shared_ptr<shared::ServerConfig> config,
                               std::shared_ptr<handlers::RTSPHandler> rtsp_handler)
    : BaseController(config), rtsp_handler_(rtsp_handler) {
}

void RTSPController::handleAddStream(const httplib::Request& req, httplib::Response& res) {
    auto start_time = std::chrono::high_resolution_clock::now();
    logRequest(req, "RTSPController::AddStream");

    try {
        // Validate request
        if (!validateJsonRequest(req, res)) {
            return;
        }

        // Parse request body
        auto request_json = parseRequestBody(req, res);
        if (request_json.isNull()) {
            return; // Error already set in parseRequestBody
        }

        // Delegate to handler
        auto response_json = rtsp_handler_->handleAddStream(request_json);

        // Send response
        sendJsonResponse(res, response_json);

    } catch (const std::exception& e) {
        logError("Add stream exception: " + std::string(e.what()), "RTSPController");
        sendErrorResponse(res, "Failed to add stream",
                         shared::AppConstants::ERROR_INTERNAL,
                         shared::AppConstants::HTTP_INTERNAL_ERROR);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    logResponse(res, "RTSPController::AddStream", duration);
}

void RTSPController::handleGetStreams(const httplib::Request& req, httplib::Response& res) {
    auto start_time = std::chrono::high_resolution_clock::now();
    logRequest(req, "RTSPController::GetStreams");

    try {
        // Delegate to handler
        auto response_json = rtsp_handler_->handleGetStreams();

        // Send response
        sendJsonResponse(res, response_json);

    } catch (const std::exception& e) {
        logError("Get streams exception: " + std::string(e.what()), "RTSPController");
        sendErrorResponse(res, "Failed to get streams",
                         shared::AppConstants::ERROR_INTERNAL,
                         shared::AppConstants::HTTP_INTERNAL_ERROR);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    logResponse(res, "RTSPController::GetStreams", duration);
}

void RTSPController::handleGetStream(const httplib::Request& req, httplib::Response& res) {
    auto start_time = std::chrono::high_resolution_clock::now();
    logRequest(req, "RTSPController::GetStream");

    try {
        // Extract stream ID from URL
        std::string stream_id = extractStreamId(req);
        if (!validateStreamIdParam(stream_id, res)) {
            return;
        }

        // Delegate to handler
        auto response_json = rtsp_handler_->handleGetStream(stream_id);

        // Send response
        sendJsonResponse(res, response_json);

    } catch (const std::exception& e) {
        logError("Get stream exception: " + std::string(e.what()), "RTSPController");
        sendErrorResponse(res, "Failed to get stream",
                         shared::AppConstants::ERROR_INTERNAL,
                         shared::AppConstants::HTTP_INTERNAL_ERROR);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    logResponse(res, "RTSPController::GetStream", duration);
}

void RTSPController::handleUpdateStream(const httplib::Request& req, httplib::Response& res) {
    auto start_time = std::chrono::high_resolution_clock::now();
    logRequest(req, "RTSPController::UpdateStream");

    try {
        // Extract stream ID from URL
        std::string stream_id = extractStreamId(req);
        if (!validateStreamIdParam(stream_id, res)) {
            return;
        }

        // Validate request
        if (!validateJsonRequest(req, res)) {
            return;
        }

        // Parse request body
        auto request_json = parseRequestBody(req, res);
        if (request_json.isNull()) {
            return; // Error already set in parseRequestBody
        }

        // Delegate to handler
        auto response_json = rtsp_handler_->handleUpdateStream(stream_id, request_json);

        // Send response
        sendJsonResponse(res, response_json);

    } catch (const std::exception& e) {
        logError("Update stream exception: " + std::string(e.what()), "RTSPController");
        sendErrorResponse(res, "Failed to update stream",
                         shared::AppConstants::ERROR_INTERNAL,
                         shared::AppConstants::HTTP_INTERNAL_ERROR);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    logResponse(res, "RTSPController::UpdateStream", duration);
}

void RTSPController::handleRemoveStream(const httplib::Request& req, httplib::Response& res) {
    auto start_time = std::chrono::high_resolution_clock::now();
    logRequest(req, "RTSPController::RemoveStream");

    try {
        // Extract stream ID from URL
        std::string stream_id = extractStreamId(req);
        if (!validateStreamIdParam(stream_id, res)) {
            return;
        }

        // Delegate to handler
        auto response_json = rtsp_handler_->handleRemoveStream(stream_id);

        // Send response
        sendJsonResponse(res, response_json);

    } catch (const std::exception& e) {
        logError("Remove stream exception: " + std::string(e.what()), "RTSPController");
        sendErrorResponse(res, "Failed to remove stream",
                         shared::AppConstants::ERROR_INTERNAL,
                         shared::AppConstants::HTTP_INTERNAL_ERROR);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    logResponse(res, "RTSPController::RemoveStream", duration);
}

void RTSPController::handleGetStreamStats(const httplib::Request& req, httplib::Response& res) {
    auto start_time = std::chrono::high_resolution_clock::now();
    logRequest(req, "RTSPController::GetStreamStats");

    try {
        // Extract stream ID from URL
        std::string stream_id = extractStreamId(req);
        if (!validateStreamIdParam(stream_id, res)) {
            return;
        }

        // Delegate to handler
        auto response_json = rtsp_handler_->handleGetStreamStats(stream_id);

        // Send response
        sendJsonResponse(res, response_json);

    } catch (const std::exception& e) {
        logError("Get stream stats exception: " + std::string(e.what()), "RTSPController");
        sendErrorResponse(res, "Failed to get stream statistics",
                         shared::AppConstants::ERROR_INTERNAL,
                         shared::AppConstants::HTTP_INTERNAL_ERROR);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    logResponse(res, "RTSPController::GetStreamStats", duration);
}

void RTSPController::handleControlStream(const httplib::Request& req, httplib::Response& res) {
    auto start_time = std::chrono::high_resolution_clock::now();
    logRequest(req, "RTSPController::ControlStream");

    try {
        // Extract stream ID from URL
        std::string stream_id = extractStreamId(req);
        if (!validateStreamIdParam(stream_id, res)) {
            return;
        }

        // Validate request
        if (!validateJsonRequest(req, res)) {
            return;
        }

        // Parse request body
        auto request_json = parseRequestBody(req, res);
        if (request_json.isNull()) {
            return; // Error already set in parseRequestBody
        }

        // Delegate to handler
        auto response_json = rtsp_handler_->handleControlStream(stream_id, request_json);

        // Send response
        sendJsonResponse(res, response_json);

    } catch (const std::exception& e) {
        logError("Control stream exception: " + std::string(e.what()), "RTSPController");
        sendErrorResponse(res, "Failed to control stream",
                         shared::AppConstants::ERROR_INTERNAL,
                         shared::AppConstants::HTTP_INTERNAL_ERROR);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    logResponse(res, "RTSPController::ControlStream", duration);
}

void RTSPController::handleGetSystemStatus(const httplib::Request& req, httplib::Response& res) {
    auto start_time = std::chrono::high_resolution_clock::now();
    logRequest(req, "RTSPController::GetSystemStatus");

    try {
        // Delegate to handler
        auto response_json = rtsp_handler_->handleGetSystemStatus();

        // Send response
        sendJsonResponse(res, response_json);

    } catch (const std::exception& e) {
        logError("Get system status exception: " + std::string(e.what()), "RTSPController");
        sendErrorResponse(res, "Failed to get system status",
                         shared::AppConstants::ERROR_INTERNAL,
                         shared::AppConstants::HTTP_INTERNAL_ERROR);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    logResponse(res, "RTSPController::GetSystemStatus", duration);
}

void RTSPController::handleGetSystemStats(const httplib::Request& req, httplib::Response& res) {
    auto start_time = std::chrono::high_resolution_clock::now();
    logRequest(req, "RTSPController::GetSystemStats");

    try {
        // Delegate to handler
        auto response_json = rtsp_handler_->handleGetSystemStats();

        // Send response
        sendJsonResponse(res, response_json);

    } catch (const std::exception& e) {
        logError("Get system stats exception: " + std::string(e.what()), "RTSPController");
        sendErrorResponse(res, "Failed to get system statistics",
                         shared::AppConstants::ERROR_INTERNAL,
                         shared::AppConstants::HTTP_INTERNAL_ERROR);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    logResponse(res, "RTSPController::GetSystemStats", duration);
}

void RTSPController::registerRoutes(httplib::Server& server) {
    // Stream management routes
    server.Post("/api/v1/rtsp/streams", [this](const httplib::Request& req, httplib::Response& res) {
        handleAddStream(req, res);
    });

    server.Get("/api/v1/rtsp/streams", [this](const httplib::Request& req, httplib::Response& res) {
        handleGetStreams(req, res);
    });

    server.Get(R"(/api/v1/rtsp/streams/([^/]+))", [this](const httplib::Request& req, httplib::Response& res) {
        handleGetStream(req, res);
    });

    server.Put(R"(/api/v1/rtsp/streams/([^/]+))", [this](const httplib::Request& req, httplib::Response& res) {
        handleUpdateStream(req, res);
    });

    server.Delete(R"(/api/v1/rtsp/streams/([^/]+))", [this](const httplib::Request& req, httplib::Response& res) {
        handleRemoveStream(req, res);
    });

    server.Get(R"(/api/v1/rtsp/streams/([^/]+)/stats)", [this](const httplib::Request& req, httplib::Response& res) {
        handleGetStreamStats(req, res);
    });

    server.Post(R"(/api/v1/rtsp/streams/([^/]+)/control)", [this](const httplib::Request& req, httplib::Response& res) {
        handleControlStream(req, res);
    });

    // System routes
    server.Get("/api/v1/rtsp/system/status", [this](const httplib::Request& req, httplib::Response& res) {
        handleGetSystemStatus(req, res);
    });

    server.Get("/api/v1/rtsp/system/stats", [this](const httplib::Request& req, httplib::Response& res) {
        handleGetSystemStats(req, res);
    });
}

// Helper methods implementation
utils::JsonValue RTSPController::parseRequestBody(const httplib::Request& req, httplib::Response& res) {
    try {
        if (req.body.empty()) {
            sendErrorResponse(res, "Request body is empty", "EMPTY_BODY", 400);
            return utils::JsonValue();
        }

        auto json_result = utils::JsonUtils::parse(req.body);
        if (!json_result.has_value()) {
            sendErrorResponse(res, "Invalid JSON in request body", "INVALID_JSON", 400);
            return utils::JsonValue();
        }

        return json_result.value();

    } catch (const std::exception& e) {
        logError("JSON parsing exception: " + std::string(e.what()), "RTSPController");
        sendErrorResponse(res, "Failed to parse JSON", "JSON_PARSE_ERROR", 400);
        return utils::JsonValue();
    }
}

void RTSPController::sendJsonResponse(httplib::Response& res, const utils::JsonValue& json_response) {
    try {
        std::string json_string = utils::JsonUtils::stringify(json_response);

        // Extract status code from response if present
        int status_code = 200;
        if (json_response.isObject()) {
            auto obj = json_response.asObject();
            if (obj.find("http_code") != obj.end() && obj.at("http_code").isNumber()) {
                status_code = static_cast<int>(obj.at("http_code").asNumber());
            }
        }

        res.status = status_code;
        res.set_content(json_string, shared::AppConstants::CONTENT_TYPE_JSON);
        setCorsHeaders(res);

    } catch (const std::exception& e) {
        logError("JSON serialization exception: " + std::string(e.what()), "RTSPController");
        sendErrorResponse(res, "Failed to serialize response",
                         shared::AppConstants::ERROR_INTERNAL,
                         shared::AppConstants::HTTP_INTERNAL_ERROR);
    }
}

std::string RTSPController::extractStreamId(const httplib::Request& req) {
    // Extract stream ID from URL path parameter
    if (req.matches.size() > 1) {
        return req.matches[1].str();
    }
    return "";
}

bool RTSPController::validateJsonRequest(const httplib::Request& req, httplib::Response& res) {
    // Validate content type
    if (!validateContentType(req)) {
        sendErrorResponse(res, "Content-Type must be application/json", "INVALID_CONTENT_TYPE", 400);
        return false;
    }

    // Validate request size
    if (!validateRequestSize(req, 1024 * 1024)) { // 1MB limit
        sendErrorResponse(res, "Request body too large", "REQUEST_TOO_LARGE", 413);
        return false;
    }

    return true;
}

bool RTSPController::validateStreamIdParam(const std::string& stream_id, httplib::Response& res) {
    if (stream_id.empty()) {
        sendErrorResponse(res, "Stream ID parameter is required", "MISSING_STREAM_ID", 400);
        return false;
    }

    // Basic validation - more detailed validation is done in the handler
    if (stream_id.length() > 64) {
        sendErrorResponse(res, "Stream ID too long", "INVALID_STREAM_ID", 400);
        return false;
    }

    return true;
}

} // namespace server::controllers
