#include <iostream>
#include <memory>
#include <string>
#include <chrono>
#include <iomanip>
#include <httplib.h>

// Include all the MVC components
#include "shared/config.hpp"
#include "repositories/ai_model_repository.hpp"
#include "services/inference_service.hpp"
#include "services/health_service.hpp"
#include "handlers/inference_handler.hpp"
#include "handlers/health_handler.hpp"
#include "controllers/inference_controller.hpp"
#include "controllers/health_controller.hpp"
#include "controllers/rtsp_controller.hpp"
#include "handlers/rtsp_handler.hpp"
#include "rtsp/stream_multiplexer.hpp"

using namespace server;

/**
 * @brief Application class that orchestrates the MVC architecture
 */
class AIBoxApplication {
private:
    std::shared_ptr<shared::ServerConfig> config_;
    std::shared_ptr<repositories::IAIModelRepository> repository_;
    std::shared_ptr<services::IInferenceService> inference_service_;
    std::shared_ptr<services::IHealthService> health_service_;
    std::shared_ptr<handlers::InferenceHandler> inference_handler_;
    std::shared_ptr<handlers::HealthHandler> health_handler_;
    std::shared_ptr<controllers::InferenceController> inference_controller_;
    std::shared_ptr<controllers::HealthController> health_controller_;
    std::shared_ptr<controllers::RTSPController> rtsp_controller_;
    std::shared_ptr<handlers::RTSPHandler> rtsp_handler_;
    std::shared_ptr<aibox::rtsp::StreamMultiplexer> stream_multiplexer_;
    std::unique_ptr<httplib::Server> server_;

public:
    explicit AIBoxApplication(std::shared_ptr<shared::ServerConfig> config)
        : config_(config) {
        initializeComponents();
        setupServer();
    }

    void run() {
        printStartupInfo();

        // Record server start time
        health_service_->recordServerStart();

        // Start the server
        std::cout << "Starting server on " << config_->host << ":" << config_->port << std::endl;

        if (!server_->listen(config_->host, config_->port)) {
            std::cerr << "Failed to start server on " << config_->host << ":" << config_->port << std::endl;
            exit(1);
        }
    }

    void stop() {
        if (server_) {
            server_->stop();
        }
    }

private:
    void initializeComponents() {
        std::cout << "[Application] Initializing MVC components..." << std::endl;

        // Initialize Repository layer
        repository_ = std::make_shared<repositories::AIModelRepository>(config_->models_base_path);

        // Initialize Services layer
        inference_service_ = std::make_shared<services::InferenceService>(repository_);
        health_service_ = std::make_shared<services::HealthService>(repository_);

        // Initialize Handlers layer
        inference_handler_ = std::make_shared<handlers::InferenceHandler>(config_, inference_service_);
        health_handler_ = std::make_shared<handlers::HealthHandler>(config_, health_service_);

        // Initialize RTSP components
        stream_multiplexer_ = std::make_shared<aibox::rtsp::StreamMultiplexer>();
        rtsp_handler_ = std::make_shared<handlers::RTSPHandler>(config_, stream_multiplexer_);

        // Initialize Controllers layer
        inference_controller_ = std::make_shared<controllers::InferenceController>(config_, inference_handler_);
        health_controller_ = std::make_shared<controllers::HealthController>(config_, health_handler_);
        rtsp_controller_ = std::make_shared<controllers::RTSPController>(config_, rtsp_handler_);

        std::cout << "[Application] MVC components initialized successfully" << std::endl;
    }

    void setupServer() {
        std::cout << "[Application] Setting up HTTP server..." << std::endl;

        server_ = std::make_unique<httplib::Server>();

        // Setup middleware
        setupMiddleware();

        // Register routes from controllers
        inference_controller_->registerRoutes(*server_);
        health_controller_->registerRoutes(*server_);
        rtsp_controller_->registerRoutes(*server_);

        // Setup error handlers
        setupErrorHandlers();

        std::cout << "[Application] HTTP server setup completed" << std::endl;
    }

    void setupMiddleware() {
        // CORS middleware
        if (config_->enable_cors) {
            server_->set_pre_routing_handler([this](const httplib::Request& /*req*/, httplib::Response& res) {
                res.set_header("Access-Control-Allow-Origin", config_->cors_origin);
                res.set_header("Access-Control-Allow-Methods", config_->cors_methods);
                res.set_header("Access-Control-Allow-Headers", config_->cors_headers);
                return httplib::Server::HandlerResponse::Unhandled;
            });
        }

        // Request logging middleware
        if (config_->enable_request_logging) {
            server_->set_logger([](const httplib::Request& req, const httplib::Response& res) {
                auto now = std::chrono::system_clock::now();
                auto time_t = std::chrono::system_clock::to_time_t(now);
                std::cout << "[" << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S")
                          << "] " << req.method << " " << req.path
                          << " - " << res.status << std::endl;
            });
        }

        // Set server timeouts
        server_->set_read_timeout(config_->timeout_seconds, 0);
        server_->set_write_timeout(config_->timeout_seconds, 0);
    }

    void setupErrorHandlers() {
        // Handle OPTIONS requests for CORS
        server_->Options(".*", [this](const httplib::Request& /*req*/, httplib::Response& res) {
            res.status = 200;
            if (config_->enable_cors) {
                res.set_header("Access-Control-Allow-Origin", config_->cors_origin);
                res.set_header("Access-Control-Allow-Methods", config_->cors_methods);
                res.set_header("Access-Control-Allow-Headers", config_->cors_headers);
            }
        });

        // 404 handler
        server_->set_error_handler([](const httplib::Request& /*req*/, httplib::Response& res) {
            if (res.status == 404) {
                std::string error_json = R"({"success":false,"error":"Endpoint not found","code":404})";
                res.set_content(error_json, "application/json");
            }
        });
    }

    void printStartupInfo() {
        std::cout << "=== C-AIBOX HTTP API Server (MVC Architecture) ===" << std::endl;
        std::cout << "Version: " << shared::AppConstants::APP_VERSION << std::endl;
        std::cout << "Host: " << config_->host << std::endl;
        std::cout << "Port: " << config_->port << std::endl;
        std::cout << "CORS: " << (config_->enable_cors ? "Enabled" : "Disabled") << std::endl;
        std::cout << "Request Logging: " << (config_->enable_request_logging ? "Enabled" : "Disabled") << std::endl;
        std::cout << "Models Path: " << config_->models_base_path << std::endl;
        std::cout << std::endl;
        std::cout << "Available endpoints:" << std::endl;
        std::cout << "  GET  /health - Health check" << std::endl;
        std::cout << "  GET  /api/v1/info - API information" << std::endl;
        std::cout << "  GET  /api/v1/status - System status" << std::endl;
        std::cout << "  POST /api/v1/arcface/infer - ArcFace inference" << std::endl;
        std::cout << "  POST /api/v1/yolo/detect - YOLO detection" << std::endl;
        std::cout << std::endl;
        std::cout << "RTSP Stream Management:" << std::endl;
        std::cout << "  POST   /api/v1/rtsp/streams - Add new stream" << std::endl;
        std::cout << "  GET    /api/v1/rtsp/streams - List all streams" << std::endl;
        std::cout << "  GET    /api/v1/rtsp/streams/{id} - Get stream details" << std::endl;
        std::cout << "  PUT    /api/v1/rtsp/streams/{id} - Update stream config" << std::endl;
        std::cout << "  DELETE /api/v1/rtsp/streams/{id} - Remove stream" << std::endl;
        std::cout << "  GET    /api/v1/rtsp/streams/{id}/stats - Get stream stats" << std::endl;
        std::cout << "  POST   /api/v1/rtsp/streams/{id}/control - Control stream" << std::endl;
        std::cout << "  GET    /api/v1/rtsp/system/status - RTSP system status" << std::endl;
        std::cout << "  GET    /api/v1/rtsp/system/stats - RTSP system stats" << std::endl;
        std::cout << "=================================================" << std::endl;
    }
};

// Command line argument parsing
std::shared_ptr<shared::ServerConfig> parseCommandLineArgs(int argc, char* argv[]) {
    auto config = std::make_shared<shared::ServerConfig>();

    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "--port" && i + 1 < argc) {
            config->port = std::stoi(argv[++i]);
        } else if (arg == "--host" && i + 1 < argc) {
            config->host = argv[++i];
        } else if (arg == "--models-path" && i + 1 < argc) {
            config->models_base_path = argv[++i];
        } else if (arg == "--disable-cors") {
            config->enable_cors = false;
        } else if (arg == "--disable-logging") {
            config->enable_request_logging = false;
        } else if (arg == "--debug") {
            config->enable_debug_logging = true;
        } else if (arg == "--help") {
            std::cout << "Usage: " << argv[0] << " [options]" << std::endl;
            std::cout << "Options:" << std::endl;
            std::cout << "  --port <port>         Server port (default: 8080)" << std::endl;
            std::cout << "  --host <host>         Server host (default: 0.0.0.0)" << std::endl;
            std::cout << "  --models-path <path>  Models base path (default: ./models)" << std::endl;
            std::cout << "  --disable-cors        Disable CORS support" << std::endl;
            std::cout << "  --disable-logging     Disable request logging" << std::endl;
            std::cout << "  --debug               Enable debug logging" << std::endl;
            std::cout << "  --help                Show this help message" << std::endl;
            exit(0);
        }
    }

    if (!config->isValid()) {
        std::cerr << "Invalid configuration parameters" << std::endl;
        exit(1);
    }

    return config;
}

int main(int argc, char* argv[]) {
    try {
        // Parse command line arguments
        auto config = parseCommandLineArgs(argc, argv);

        // Create and run application
        AIBoxApplication app(config);
        app.run();

    } catch (const std::exception& e) {
        std::cerr << "Application error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
