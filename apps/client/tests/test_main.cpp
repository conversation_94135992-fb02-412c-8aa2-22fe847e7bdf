#include <QApplication>
#include <QTest>
#include <QDebug>
#include <QDir>
#include <QStandardPaths>

// Test includes will be added by each test executable

/**
 * @brief Main test runner for C-AIBOX Client Application Tests
 * 
 * This provides a common test runner for all client application test suites.
 * Each test executable will link against this main function.
 */

class TestEnvironment
{
public:
    static void setupTestEnvironment()
    {
        // Set up test-specific application properties
        QCoreApplication::setApplicationName("C-AIBOX-Client-Tests");
        QCoreApplication::setApplicationVersion("1.0.0");
        QCoreApplication::setOrganizationName("C-AIBOX");
        QCoreApplication::setOrganizationDomain("c-aibox.com");
        
        // Set up test data directory
        QString testDataDir = QDir::currentPath() + "/test_data";
        if (!QDir(testDataDir).exists()) {
            QDir().mkpath(testDataDir);
        }
        
        // Set environment variables for testing
        qputenv("QT_QPA_PLATFORM", "offscreen"); // For headless testing
        qputenv("CAIBOX_TEST_MODE", "1");
        qputenv("CAIBOX_TEST_DATA_DIR", testDataDir.toUtf8());
        
        qDebug() << "Test environment initialized";
        qDebug() << "Test data directory:" << testDataDir;
    }
    
    static void cleanupTestEnvironment()
    {
        // Clean up any test artifacts
        QString testDataDir = QDir::currentPath() + "/test_data";
        QDir(testDataDir).removeRecursively();
        
        qDebug() << "Test environment cleaned up";
    }
};

// This will be defined by each test executable
extern QList<QObject*> createTestObjects();

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Initialize test environment
    TestEnvironment::setupTestEnvironment();
    
    int result = 0;
    
    try {
        // Get test objects from the specific test executable
        QList<QObject*> testObjects = createTestObjects();
        
        // Run all tests
        for (QObject* testObject : testObjects) {
            if (testObject) {
                qDebug() << "Running test:" << testObject->metaObject()->className();
                int testResult = QTest::qExec(testObject, argc, argv);
                if (testResult != 0) {
                    result = testResult;
                    qWarning() << "Test failed:" << testObject->metaObject()->className();
                }
                delete testObject;
            }
        }
        
        if (result == 0) {
            qDebug() << "All tests passed successfully!";
        } else {
            qWarning() << "Some tests failed. Exit code:" << result;
        }
        
    } catch (const std::exception& e) {
        qCritical() << "Test execution failed with exception:" << e.what();
        result = -1;
    } catch (...) {
        qCritical() << "Test execution failed with unknown exception";
        result = -1;
    }
    
    // Cleanup test environment
    TestEnvironment::cleanupTestEnvironment();
    
    return result;
}
