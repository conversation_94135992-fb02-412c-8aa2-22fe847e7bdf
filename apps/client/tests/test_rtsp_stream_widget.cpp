#include <QTest>
#include <QApplication>
#include <QSignalSpy>
#include <QTimer>
#include <QPixmap>
#include <QPainter>
#include <QMouseEvent>
#include <QContextMenuEvent>
#include <QDebug>

// Mock the RTSP Stream Widget since we don't have the actual implementation
// This simulates the interface based on the header file we saw

struct FaceDetection {
    QRect boundingBox;
    QString name;
    float confidence;
    QString status; // "authorized", "unauthorized", "unknown"
    
    FaceDetection(const QRect& box = QRect(), const QString& n = "", float conf = 0.0f, const QString& s = "unknown")
        : boundingBox(box), name(n), confidence(conf), status(s) {}
};

struct StreamStats {
    QString streamUrl;
    QString status;
    int fps;
    int bitrate;
    int frameCount;
    int droppedFrames;
    float cpuUsage;
    float memoryUsage;
    QDateTime lastUpdate;
    
    StreamStats() : fps(0), bitrate(0), frameCount(0), droppedFrames(0), 
                   cpuUsage(0.0f), memoryUsage(0.0f) {}
};

// Mock RTSPStreamWidget for testing
class MockRTSPStreamWidget : public QFrame
{
    Q_OBJECT

public:
    explicit MockRTSPStreamWidget(int streamId, const QString& streamUrl, QWidget *parent = nullptr)
        : QFrame(parent), m_streamId(streamId), m_streamUrl(streamUrl), m_isStreaming(false),
          m_showOverlays(true), m_showStatistics(true), m_frameCounter(0)
    {
        setMinimumSize(320, 240);
        setFrameStyle(QFrame::Box | QFrame::Raised);
        
        // Initialize mock data
        m_stats.streamUrl = streamUrl;
        m_stats.status = "Disconnected";
        m_stats.fps = 0;
        m_stats.bitrate = 0;
        m_stats.frameCount = 0;
        
        // Setup update timer
        m_updateTimer = new QTimer(this);
        connect(m_updateTimer, &QTimer::timeout, this, &MockRTSPStreamWidget::updateMockFrame);
    }

    // Stream control
    void startStream() {
        if (!m_isStreaming) {
            m_isStreaming = true;
            m_stats.status = "Streaming";
            m_stats.fps = 30;
            m_stats.bitrate = 2000;
            m_updateTimer->start(33); // ~30 FPS
            emit streamStatusChanged(m_streamId, "Streaming");
        }
    }
    
    void stopStream() {
        if (m_isStreaming) {
            m_isStreaming = false;
            m_stats.status = "Stopped";
            m_stats.fps = 0;
            m_updateTimer->stop();
            emit streamStatusChanged(m_streamId, "Stopped");
        }
    }
    
    void restartStream() {
        stopStream();
        QTimer::singleShot(100, this, &MockRTSPStreamWidget::startStream);
    }
    
    bool isStreaming() const { return m_isStreaming; }
    
    // Stream properties
    int getStreamId() const { return m_streamId; }
    QString getStreamUrl() const { return m_streamUrl; }
    void setStreamUrl(const QString& url) { 
        m_streamUrl = url;
        m_stats.streamUrl = url;
    }
    
    // Display settings
    void setShowOverlays(bool show) { m_showOverlays = show; update(); }
    void setShowStatistics(bool show) { m_showStatistics = show; update(); }
    void setStreamTitle(const QString& title) { m_streamTitle = title; update(); }
    
    // Mock data for testing
    void addMockFaceDetection(const FaceDetection& face) {
        m_faceDetections.append(face);
        update();
    }
    
    void clearFaceDetections() {
        m_faceDetections.clear();
        update();
    }
    
    void updateMockFrame() {
        m_frameCounter++;
        m_stats.frameCount = m_frameCounter;
        m_stats.lastUpdate = QDateTime::currentDateTime();
        update();
    }
    
    // Test accessors
    const QList<FaceDetection>& getFaceDetections() const { return m_faceDetections; }
    const StreamStats& getStats() const { return m_stats; }
    bool getShowOverlays() const { return m_showOverlays; }
    bool getShowStatistics() const { return m_showStatistics; }
    int getFrameCounter() const { return m_frameCounter; }

signals:
    void streamClicked(int streamId);
    void streamDoubleClicked(int streamId);
    void streamContextMenu(int streamId, const QPoint& position);
    void streamStatusChanged(int streamId, const QString& status);

protected:
    void mousePressEvent(QMouseEvent* event) override {
        QFrame::mousePressEvent(event);
        if (event->button() == Qt::LeftButton) {
            emit streamClicked(m_streamId);
        }
    }
    
    void mouseDoubleClickEvent(QMouseEvent* event) override {
        QFrame::mouseDoubleClickEvent(event);
        if (event->button() == Qt::LeftButton) {
            emit streamDoubleClicked(m_streamId);
        }
    }
    
    void contextMenuEvent(QContextMenuEvent* event) override {
        QFrame::contextMenuEvent(event);
        emit streamContextMenu(m_streamId, event->pos());
    }
    
    void paintEvent(QPaintEvent* event) override {
        QFrame::paintEvent(event);
        
        QPainter painter(this);
        painter.setRenderHint(QPainter::Antialiasing);
        
        // Draw mock video background
        QRect videoRect = rect().adjusted(10, 30, -10, -50);
        painter.fillRect(videoRect, QColor(30, 42, 68));
        
        // Draw face overlays if enabled
        if (m_showOverlays) {
            for (const auto& face : m_faceDetections) {
                QColor color = (face.status == "authorized") ? Qt::green : 
                              (face.status == "unauthorized") ? Qt::red : Qt::yellow;
                painter.setPen(QPen(color, 2));
                painter.setBrush(Qt::NoBrush);
                painter.drawRect(face.boundingBox);
                
                QString label = QString("%1 (%2%)").arg(face.name).arg(int(face.confidence * 100));
                painter.drawText(face.boundingBox.adjusted(0, -20, 0, 0), Qt::AlignBottom | Qt::AlignLeft, label);
            }
        }
        
        // Draw statistics if enabled
        if (m_showStatistics) {
            painter.setPen(Qt::white);
            painter.setFont(QFont("Arial", 8));
            QString stats = QString("FPS: %1\nBitrate: %2 kbps\nFrames: %3")
                           .arg(m_stats.fps)
                           .arg(m_stats.bitrate)
                           .arg(m_stats.frameCount);
            painter.drawText(videoRect.adjusted(10, 10, -10, -10), Qt::AlignBottom | Qt::AlignLeft, stats);
        }
    }

private:
    int m_streamId;
    QString m_streamUrl;
    QString m_streamTitle;
    bool m_isStreaming;
    bool m_showOverlays;
    bool m_showStatistics;
    int m_frameCounter;
    
    QList<FaceDetection> m_faceDetections;
    StreamStats m_stats;
    QTimer* m_updateTimer;
};

class TestRTSPStreamWidget : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();
    
    // Basic functionality tests
    void testWidgetCreation();
    void testStreamProperties();
    void testStreamControl();
    void testDisplaySettings();
    
    // Face detection tests
    void testFaceDetectionOverlays();
    void testFaceDetectionClear();
    void testMultipleFaces();
    
    // Statistics tests
    void testStatisticsDisplay();
    void testStatisticsUpdate();
    
    // Interaction tests
    void testMouseEvents();
    void testSignalEmission();
    
    // Performance tests
    void testAnimationPerformance();
    void testMemoryUsage();

private:
    MockRTSPStreamWidget* m_widget;
    QApplication* m_app;
};

void TestRTSPStreamWidget::initTestCase()
{
    qDebug() << "Starting RTSP Stream Widget tests";
}

void TestRTSPStreamWidget::cleanupTestCase()
{
    qDebug() << "RTSP Stream Widget tests completed";
}

void TestRTSPStreamWidget::init()
{
    m_widget = new MockRTSPStreamWidget(1, "rtsp://test.camera.com/stream1");
    QVERIFY(m_widget != nullptr);
}

void TestRTSPStreamWidget::cleanup()
{
    delete m_widget;
    m_widget = nullptr;
}

void TestRTSPStreamWidget::testWidgetCreation()
{
    QCOMPARE(m_widget->getStreamId(), 1);
    QCOMPARE(m_widget->getStreamUrl(), QString("rtsp://test.camera.com/stream1"));
    QVERIFY(!m_widget->isStreaming());
    QVERIFY(m_widget->getShowOverlays());
    QVERIFY(m_widget->getShowStatistics());
    QCOMPARE(m_widget->minimumSize(), QSize(320, 240));
}

void TestRTSPStreamWidget::testStreamProperties()
{
    // Test URL change
    m_widget->setStreamUrl("rtsp://new.camera.com/stream2");
    QCOMPARE(m_widget->getStreamUrl(), QString("rtsp://new.camera.com/stream2"));
    
    // Test title change
    m_widget->setStreamTitle("Test Camera");
    // Title is set but we don't have a getter in our mock, so just verify no crash
    QVERIFY(true);
}

void TestRTSPStreamWidget::testStreamControl()
{
    QSignalSpy statusSpy(m_widget, &MockRTSPStreamWidget::streamStatusChanged);
    
    // Test start stream
    QVERIFY(!m_widget->isStreaming());
    m_widget->startStream();
    QVERIFY(m_widget->isStreaming());
    QCOMPARE(statusSpy.count(), 1);
    QCOMPARE(statusSpy.at(0).at(0).toInt(), 1);
    QCOMPARE(statusSpy.at(0).at(1).toString(), QString("Streaming"));
    
    // Test stop stream
    m_widget->stopStream();
    QVERIFY(!m_widget->isStreaming());
    QCOMPARE(statusSpy.count(), 2);
    QCOMPARE(statusSpy.at(1).at(1).toString(), QString("Stopped"));
    
    // Test restart stream
    m_widget->restartStream();
    // Wait for restart to complete
    QTest::qWait(150);
    QVERIFY(m_widget->isStreaming());
}

void TestRTSPStreamWidget::testDisplaySettings()
{
    // Test overlay toggle
    QVERIFY(m_widget->getShowOverlays());
    m_widget->setShowOverlays(false);
    QVERIFY(!m_widget->getShowOverlays());
    
    // Test statistics toggle
    QVERIFY(m_widget->getShowStatistics());
    m_widget->setShowStatistics(false);
    QVERIFY(!m_widget->getShowStatistics());
}

void TestRTSPStreamWidget::testFaceDetectionOverlays()
{
    FaceDetection face1(QRect(50, 50, 80, 100), "John Doe", 0.95f, "authorized");
    
    QCOMPARE(m_widget->getFaceDetections().size(), 0);
    
    m_widget->addMockFaceDetection(face1);
    QCOMPARE(m_widget->getFaceDetections().size(), 1);
    
    const FaceDetection& addedFace = m_widget->getFaceDetections().at(0);
    QCOMPARE(addedFace.name, QString("John Doe"));
    QCOMPARE(addedFace.confidence, 0.95f);
    QCOMPARE(addedFace.status, QString("authorized"));
    QCOMPARE(addedFace.boundingBox, QRect(50, 50, 80, 100));
}

void TestRTSPStreamWidget::testFaceDetectionClear()
{
    // Add some faces
    m_widget->addMockFaceDetection(FaceDetection(QRect(10, 10, 50, 60), "Person 1", 0.8f, "authorized"));
    m_widget->addMockFaceDetection(FaceDetection(QRect(100, 100, 50, 60), "Person 2", 0.7f, "unauthorized"));
    
    QCOMPARE(m_widget->getFaceDetections().size(), 2);
    
    // Clear faces
    m_widget->clearFaceDetections();
    QCOMPARE(m_widget->getFaceDetections().size(), 0);
}

void TestRTSPStreamWidget::testMultipleFaces()
{
    QList<FaceDetection> faces = {
        FaceDetection(QRect(10, 10, 50, 60), "Alice", 0.95f, "authorized"),
        FaceDetection(QRect(100, 10, 50, 60), "Bob", 0.88f, "authorized"),
        FaceDetection(QRect(200, 10, 50, 60), "Unknown", 0.65f, "unknown"),
        FaceDetection(QRect(10, 100, 50, 60), "Intruder", 0.92f, "unauthorized")
    };
    
    for (const auto& face : faces) {
        m_widget->addMockFaceDetection(face);
    }
    
    QCOMPARE(m_widget->getFaceDetections().size(), 4);
    
    // Verify each face
    const auto& detectedFaces = m_widget->getFaceDetections();
    QCOMPARE(detectedFaces[0].name, QString("Alice"));
    QCOMPARE(detectedFaces[1].name, QString("Bob"));
    QCOMPARE(detectedFaces[2].name, QString("Unknown"));
    QCOMPARE(detectedFaces[3].name, QString("Intruder"));
}

void TestRTSPStreamWidget::testStatisticsDisplay()
{
    const StreamStats& stats = m_widget->getStats();
    
    // Initial state
    QCOMPARE(stats.fps, 0);
    QCOMPARE(stats.bitrate, 0);
    QCOMPARE(stats.frameCount, 0);
    QCOMPARE(stats.status, QString("Disconnected"));
    
    // After starting stream
    m_widget->startStream();
    const StreamStats& streamingStats = m_widget->getStats();
    QCOMPARE(streamingStats.fps, 30);
    QCOMPARE(streamingStats.bitrate, 2000);
    QCOMPARE(streamingStats.status, QString("Streaming"));
}

void TestRTSPStreamWidget::testStatisticsUpdate()
{
    m_widget->startStream();
    
    int initialFrameCount = m_widget->getFrameCounter();
    
    // Wait for a few frame updates
    QTest::qWait(100);
    
    int updatedFrameCount = m_widget->getFrameCounter();
    QVERIFY(updatedFrameCount > initialFrameCount);
}

void TestRTSPStreamWidget::testMouseEvents()
{
    QSignalSpy clickSpy(m_widget, &MockRTSPStreamWidget::streamClicked);
    QSignalSpy doubleClickSpy(m_widget, &MockRTSPStreamWidget::streamDoubleClicked);
    QSignalSpy contextMenuSpy(m_widget, &MockRTSPStreamWidget::streamContextMenu);
    
    // Test single click
    QTest::mouseClick(m_widget, Qt::LeftButton, Qt::NoModifier, QPoint(50, 50));
    QCOMPARE(clickSpy.count(), 1);
    QCOMPARE(clickSpy.at(0).at(0).toInt(), 1);
    
    // Test double click
    QTest::mouseDClick(m_widget, Qt::LeftButton, Qt::NoModifier, QPoint(50, 50));
    QCOMPARE(doubleClickSpy.count(), 1);
    QCOMPARE(doubleClickSpy.at(0).at(0).toInt(), 1);
    
    // Test context menu (right click)
    QTest::mouseClick(m_widget, Qt::RightButton, Qt::NoModifier, QPoint(75, 75));
    QCOMPARE(contextMenuSpy.count(), 1);
    QCOMPARE(contextMenuSpy.at(0).at(0).toInt(), 1);
}

void TestRTSPStreamWidget::testSignalEmission()
{
    QSignalSpy statusSpy(m_widget, &MockRTSPStreamWidget::streamStatusChanged);
    
    // Test multiple status changes
    m_widget->startStream();
    m_widget->stopStream();
    m_widget->startStream();
    
    QCOMPARE(statusSpy.count(), 3);
    QCOMPARE(statusSpy.at(0).at(1).toString(), QString("Streaming"));
    QCOMPARE(statusSpy.at(1).at(1).toString(), QString("Stopped"));
    QCOMPARE(statusSpy.at(2).at(1).toString(), QString("Streaming"));
}

void TestRTSPStreamWidget::testAnimationPerformance()
{
    m_widget->startStream();
    
    QTime timer;
    timer.start();
    
    int frameCount = m_widget->getFrameCounter();
    
    // Wait for 1 second of animation
    QTest::qWait(1000);
    
    int newFrameCount = m_widget->getFrameCounter();
    int framesRendered = newFrameCount - frameCount;
    
    // Should have rendered approximately 30 frames (30 FPS)
    QVERIFY(framesRendered >= 25 && framesRendered <= 35);
    
    qDebug() << "Animation performance: " << framesRendered << "frames in 1 second";
}

void TestRTSPStreamWidget::testMemoryUsage()
{
    // Add many face detections to test memory usage
    for (int i = 0; i < 100; ++i) {
        FaceDetection face(QRect(i % 10 * 30, i % 10 * 30, 50, 60), 
                          QString("Person %1").arg(i), 
                          0.5f + (i % 50) * 0.01f, 
                          (i % 3 == 0) ? "authorized" : "unknown");
        m_widget->addMockFaceDetection(face);
    }
    
    QCOMPARE(m_widget->getFaceDetections().size(), 100);
    
    // Clear and verify memory is released
    m_widget->clearFaceDetections();
    QCOMPARE(m_widget->getFaceDetections().size(), 0);
}

// Test object creation function for the test runner
QList<QObject*> createTestObjects()
{
    return QList<QObject*>() << new TestRTSPStreamWidget();
}

#include "test_rtsp_stream_widget.moc"
