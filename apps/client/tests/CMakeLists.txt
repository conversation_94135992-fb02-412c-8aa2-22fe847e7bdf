# Client Application Test Suite
cmake_minimum_required(VERSION 3.18)

# Enable testing
enable_testing()

# Find required packages
find_package(Qt5 REQUIRED COMPONENTS Core Widgets Test Network)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_SOURCE_DIR}/../src
    ${CMAKE_SOURCE_DIR}/libraries/shared/include
)

# Common test libraries
set(TEST_LIBRARIES
    Qt5::Core
    Qt5::Widgets
    Qt5::Test
    Qt5::Network
    shared
)

# Test source files
set(WIDGET_TEST_SOURCES
    test_rtsp_stream_widget.cpp
    test_stream_control_panel.cpp
    test_single_stream_display.cpp
    test_face_recognition_sidebar.cpp
)

set(UI_TEST_SOURCES
    test_main_window.cpp
    test_single_stream_main_window.cpp
    test_configuration_dialog.cpp
)

set(INTEGRATION_TEST_SOURCES
    test_multi_stream_integration.cpp
    test_face_detection_integration.cpp
    test_network_integration.cpp
)

set(PERFORMANCE_TEST_SOURCES
    test_animation_performance.cpp
    test_memory_usage.cpp
    test_cpu_efficiency.cpp
)

set(HARDWARE_TEST_SOURCES
    test_orange_pi_deployment.cpp
    test_real_rtsp_streams.cpp
    test_device_compatibility.cpp
)

# Widget Tests
add_executable(test_widgets
    ${WIDGET_TEST_SOURCES}
    test_main.cpp
)

target_link_libraries(test_widgets PRIVATE ${TEST_LIBRARIES})

set_target_properties(test_widgets PROPERTIES
    AUTOMOC ON
    AUTOUIC ON
    AUTORCC ON
)

# UI Tests
add_executable(test_ui
    ${UI_TEST_SOURCES}
    test_main.cpp
)

target_link_libraries(test_ui PRIVATE ${TEST_LIBRARIES})

set_target_properties(test_ui PROPERTIES
    AUTOMOC ON
    AUTOUIC ON
    AUTORCC ON
)

# Integration Tests
add_executable(test_integration
    ${INTEGRATION_TEST_SOURCES}
    test_main.cpp
)

target_link_libraries(test_integration PRIVATE ${TEST_LIBRARIES})

set_target_properties(test_integration PROPERTIES
    AUTOMOC ON
    AUTOUIC ON
    AUTORCC ON
)

# Performance Tests
add_executable(test_performance
    ${PERFORMANCE_TEST_SOURCES}
    test_main.cpp
)

target_link_libraries(test_performance PRIVATE ${TEST_LIBRARIES})

set_target_properties(test_performance PROPERTIES
    AUTOMOC ON
    AUTOUIC ON
    AUTORCC ON
)

# Hardware Tests
add_executable(test_hardware
    ${HARDWARE_TEST_SOURCES}
    test_main.cpp
)

target_link_libraries(test_hardware PRIVATE ${TEST_LIBRARIES})

set_target_properties(test_hardware PROPERTIES
    AUTOMOC ON
    AUTOUIC ON
    AUTORCC ON
)

# Add tests to CTest
add_test(NAME WidgetTests COMMAND test_widgets)
add_test(NAME UITests COMMAND test_ui)
add_test(NAME IntegrationTests COMMAND test_integration)
add_test(NAME PerformanceTests COMMAND test_performance)
add_test(NAME HardwareTests COMMAND test_hardware)

# Test properties
set_tests_properties(WidgetTests PROPERTIES TIMEOUT 60)
set_tests_properties(UITests PROPERTIES TIMEOUT 120)
set_tests_properties(IntegrationTests PROPERTIES TIMEOUT 180)
set_tests_properties(PerformanceTests PROPERTIES TIMEOUT 300)
set_tests_properties(HardwareTests PROPERTIES TIMEOUT 600)

# Copy test data
file(COPY test_data DESTINATION ${CMAKE_CURRENT_BINARY_DIR})

# Install test executables
install(TARGETS test_widgets test_ui test_integration test_performance test_hardware
        DESTINATION bin/tests)
