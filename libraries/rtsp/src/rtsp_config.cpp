#include "rtsp/rtsp_config.hpp"
#include <fstream>
#include <sstream>
#include <sys/sysinfo.h>
#include <unistd.h>

// Conditional JSON support - check for nlohmann/json availability
#if defined(NLOHMANN_JSON_VERSION_MAJOR) || __has_include(<nlohmann/json.hpp>)
#include <nlohmann/json.hpp>
using json = nlohmann::json;
#define RTSP_HAS_JSON 1
#else
#define RTSP_HAS_JSON 0
#endif

namespace aibox {
namespace rtsp {

// RTSPModuleConfig implementation
void RTSPModuleConfig::autoConfigurePlatform() {
    if (!auto_detect_platform && !platform_override.empty()) {
        // Use manual override
        if (platform_override == "4gb") {
            performance.configureFor4GB();
        } else if (platform_override == "8gb") {
            performance.configureFor8GB();
        }
        return;
    }
    
    // Auto-detect based on available memory
    struct sysinfo info;
    if (sysinfo(&info) == 0) {
        // Convert to MB
        unsigned long total_memory_mb = info.totalram / (1024 * 1024);

        if (total_memory_mb >= 14000) {  // 16GB system (accounting for kernel usage)
            performance.configureFor16GB();
        } else if (total_memory_mb >= 7000) {  // 8GB system (accounting for kernel usage)
            performance.configureFor8GB();
        } else {
            performance.configureFor4GB();
        }
    } else {
        // Fallback to conservative 4GB configuration
        performance.configureFor4GB();
    }
}

// RTSPConfigManager implementation
bool RTSPConfigManager::loadFromFile(const std::string& file_path, RTSPModuleConfig& config, std::string& error) {
    try {
        std::ifstream file(file_path);
        if (!file.is_open()) {
            error = "Failed to open config file: " + file_path;
            return false;
        }

        std::string content((std::istreambuf_iterator<char>(file)),
                           std::istreambuf_iterator<char>());
        file.close();

        return loadFromJson(content, config, error);
    } catch (const std::exception& e) {
        error = "Failed to read config file: " + std::string(e.what());
        return false;
    }
}

bool RTSPConfigManager::saveToFile(const RTSPModuleConfig& config, const std::string& file_path, std::string& error) {
    try {
        std::string json_string;
        if (!toJson(config, json_string, error)) {
            return false;
        }

        std::ofstream file(file_path);
        if (!file.is_open()) {
            error = "Failed to create config file: " + file_path;
            return false;
        }

        file << json_string;
        file.close();

        return true;
    } catch (const std::exception& e) {
        error = "Failed to write config file: " + std::string(e.what());
        return false;
    }
}

bool RTSPConfigManager::loadFromJson(const std::string& json_string, RTSPModuleConfig& config, std::string& error) {
#if RTSP_HAS_JSON
    try {
        json j = json::parse(json_string);

        // Parse main module settings
        if (j.contains("rtsp_module")) {
            auto& module = j["rtsp_module"];

            if (module.contains("version")) config.version = module["version"];
            if (module.contains("enabled")) config.enabled = module["enabled"];
            if (module.contains("auto_detect_platform")) config.auto_detect_platform = module["auto_detect_platform"];
            if (module.contains("platform_override")) config.platform_override = module["platform_override"];

            // Parse performance settings
            if (module.contains("performance")) {
                auto& perf = module["performance"];
                if (perf.contains("max_concurrent_streams")) config.performance.max_concurrent_streams = perf["max_concurrent_streams"];
                if (perf.contains("thread_pool_size")) config.performance.thread_pool_size = perf["thread_pool_size"];
                if (perf.contains("max_memory_usage_mb")) config.performance.max_memory_usage_mb = perf["max_memory_usage_mb"];
                if (perf.contains("cpu_usage_limit_percent")) config.performance.cpu_usage_limit_percent = perf["cpu_usage_limit_percent"];
                if (perf.contains("enable_mpp_decoder")) config.performance.enable_mpp_decoder = perf["enable_mpp_decoder"];
                if (perf.contains("enable_rga_scaler")) config.performance.enable_rga_scaler = perf["enable_rga_scaler"];
                if (perf.contains("thermal_management")) config.performance.thermal_management = perf["thermal_management"];

                if (perf.contains("cpu_affinity") && perf["cpu_affinity"].is_array()) {
                    config.performance.cpu_affinity.clear();
                    for (auto& core : perf["cpu_affinity"]) {
                        config.performance.cpu_affinity.push_back(core);
                    }
                }
            }

            // Parse network settings
            if (module.contains("network")) {
                auto& net = module["network"];
                if (net.contains("prefer_tcp")) config.network.prefer_tcp = net["prefer_tcp"];
                if (net.contains("tcp_nodelay")) config.network.tcp_nodelay = net["tcp_nodelay"];
                if (net.contains("udp_buffer_size")) config.network.udp_buffer_size = net["udp_buffer_size"];
                if (net.contains("keep_alive_interval_ms")) config.network.keep_alive_interval_ms = net["keep_alive_interval_ms"];
                if (net.contains("ssl_verify_peer")) config.network.ssl_verify_peer = net["ssl_verify_peer"];
            }

            // Parse monitoring settings
            if (module.contains("monitoring")) {
                auto& mon = module["monitoring"];
                if (mon.contains("enable_statistics")) config.monitoring.enable_statistics = mon["enable_statistics"];
                if (mon.contains("statistics_interval_ms")) config.monitoring.statistics_interval_ms = mon["statistics_interval_ms"];
                if (mon.contains("log_level")) config.monitoring.log_level = mon["log_level"];
                if (mon.contains("log_file_path")) config.monitoring.log_file_path = mon["log_file_path"];
                if (mon.contains("enable_performance_monitoring")) config.monitoring.enable_performance_monitoring = mon["enable_performance_monitoring"];
            }
        }

        // Parse streams
        if (j.contains("streams") && j["streams"].is_array()) {
            config.streams.clear();
            for (auto& stream_json : j["streams"]) {
                RTSPConnectionConfig stream;

                if (stream_json.contains("rtsp_url")) stream.rtsp_url = stream_json["rtsp_url"];
                if (stream_json.contains("username")) stream.username = stream_json["username"];
                if (stream_json.contains("password")) stream.password = stream_json["password"];
                if (stream_json.contains("enabled")) stream.enabled = stream_json["enabled"];
                if (stream_json.contains("timeout_ms")) stream.timeout_ms = stream_json["timeout_ms"];
                if (stream_json.contains("retry_count")) stream.retry_count = stream_json["retry_count"];
                if (stream_json.contains("buffer_size_bytes")) stream.buffer_size_bytes = stream_json["buffer_size_bytes"];
                if (stream_json.contains("queue_size")) stream.queue_size = stream_json["queue_size"];
                if (stream_json.contains("use_mpp_decoder")) stream.use_mpp_decoder = stream_json["use_mpp_decoder"];
                if (stream_json.contains("use_rga_scaler")) stream.use_rga_scaler = stream_json["use_rga_scaler"];

                // Parse transport protocol
                if (stream_json.contains("transport")) {
                    std::string transport = stream_json["transport"];
                    if (transport == "tcp") stream.transport = TransportProtocol::TCP;
                    else if (transport == "udp") stream.transport = TransportProtocol::UDP;
                    else stream.transport = TransportProtocol::AUTO;
                }

                // Parse priority
                if (stream_json.contains("priority")) {
                    std::string priority = stream_json["priority"];
                    if (priority == "low") stream.priority = StreamPriority::LOW;
                    else if (priority == "medium") stream.priority = StreamPriority::MEDIUM;
                    else if (priority == "high") stream.priority = StreamPriority::HIGH;
                    else if (priority == "critical") stream.priority = StreamPriority::CRITICAL;
                }

                // Parse metadata
                if (stream_json.contains("metadata") && stream_json["metadata"].is_object()) {
                    for (auto& [key, value] : stream_json["metadata"].items()) {
                        if (value.is_string()) {
                            stream.metadata[key] = value;
                        }
                    }
                }

                config.streams.push_back(stream);
            }
        }

        // Auto-configure platform if needed
        config.autoConfigurePlatform();

        // Validate configuration
        if (!validate(config, error)) {
            return false;
        }

        return true;

    } catch (const json::parse_error& e) {
        error = "JSON parse error: " + std::string(e.what());
        return false;
    } catch (const std::exception& e) {
        error = "Configuration error: " + std::string(e.what());
        return false;
    }
#else
    error = "JSON support not available";
    return false;
#endif
}

bool RTSPConfigManager::toJson(const RTSPModuleConfig& config, std::string& json_string, std::string& error) {
#if RTSP_HAS_JSON
    try {
        json j;
        
        // Main module settings
        j["rtsp_module"]["version"] = config.version;
        j["rtsp_module"]["enabled"] = config.enabled;
        j["rtsp_module"]["auto_detect_platform"] = config.auto_detect_platform;
        j["rtsp_module"]["platform_override"] = config.platform_override;
        
        // Performance settings
        auto& perf = j["rtsp_module"]["performance"];
        perf["max_concurrent_streams"] = config.performance.max_concurrent_streams;
        perf["thread_pool_size"] = config.performance.thread_pool_size;
        perf["max_memory_usage_mb"] = config.performance.max_memory_usage_mb;
        perf["cpu_usage_limit_percent"] = config.performance.cpu_usage_limit_percent;
        perf["enable_mpp_decoder"] = config.performance.enable_mpp_decoder;
        perf["enable_rga_scaler"] = config.performance.enable_rga_scaler;
        perf["thermal_management"] = config.performance.thermal_management;
        perf["cpu_affinity"] = config.performance.cpu_affinity;
        
        // Network settings
        auto& net = j["rtsp_module"]["network"];
        net["prefer_tcp"] = config.network.prefer_tcp;
        net["tcp_nodelay"] = config.network.tcp_nodelay;
        net["udp_buffer_size"] = config.network.udp_buffer_size;
        net["keep_alive_interval_ms"] = config.network.keep_alive_interval_ms;
        net["ssl_verify_peer"] = config.network.ssl_verify_peer;
        
        // Monitoring settings
        auto& mon = j["rtsp_module"]["monitoring"];
        mon["enable_statistics"] = config.monitoring.enable_statistics;
        mon["statistics_interval_ms"] = config.monitoring.statistics_interval_ms;
        mon["log_level"] = config.monitoring.log_level;
        mon["log_file_path"] = config.monitoring.log_file_path;
        mon["enable_performance_monitoring"] = config.monitoring.enable_performance_monitoring;
        
        // Streams
        j["streams"] = json::array();
        for (const auto& stream : config.streams) {
            json stream_json;
            stream_json["rtsp_url"] = stream.rtsp_url;
            stream_json["username"] = stream.username;
            stream_json["password"] = stream.password;
            stream_json["enabled"] = stream.enabled;
            stream_json["timeout_ms"] = stream.timeout_ms;
            stream_json["retry_count"] = stream.retry_count;
            stream_json["buffer_size_bytes"] = stream.buffer_size_bytes;
            stream_json["queue_size"] = stream.queue_size;
            stream_json["use_mpp_decoder"] = stream.use_mpp_decoder;
            stream_json["use_rga_scaler"] = stream.use_rga_scaler;
            
            // Transport protocol
            switch (stream.transport) {
                case TransportProtocol::TCP: stream_json["transport"] = "tcp"; break;
                case TransportProtocol::UDP: stream_json["transport"] = "udp"; break;
                case TransportProtocol::AUTO: stream_json["transport"] = "auto"; break;
            }
            
            // Priority
            switch (stream.priority) {
                case StreamPriority::LOW: stream_json["priority"] = "low"; break;
                case StreamPriority::MEDIUM: stream_json["priority"] = "medium"; break;
                case StreamPriority::HIGH: stream_json["priority"] = "high"; break;
                case StreamPriority::CRITICAL: stream_json["priority"] = "critical"; break;
            }
            
            // Metadata
            if (!stream.metadata.empty()) {
                stream_json["metadata"] = stream.metadata;
            }
            
            j["streams"].push_back(stream_json);
        }

        json_string = j.dump(2);  // Pretty print with 2 spaces
        return true;

    } catch (const std::exception& e) {
        error = "JSON serialization error: " + std::string(e.what());
        return false;
    }
#else
    error = "JSON support not available";
    return false;
#endif
}

bool RTSPConfigManager::validate(const RTSPModuleConfig& config, std::string& error) {
    // Basic validation
    if (!config.isValid()) {
        error = "Invalid configuration: resource limits exceeded";
        return false;
    }

    // Validate individual streams
    for (const auto& stream : config.streams) {
        if (!validateStreamConfig(stream)) {
            error = "Invalid stream configuration: " + stream.rtsp_url;
            return false;
        }
    }

    // Validate performance configuration
    if (!validatePerformanceConfig(config.performance)) {
        error = "Invalid performance configuration";
        return false;
    }

    return true;
}

RTSPModuleConfig RTSPConfigManager::createDefault() {
    RTSPModuleConfig config;
    config.autoConfigurePlatform();
    return config;
}

RTSPModuleConfig RTSPConfigManager::createFor4GB() {
    RTSPModuleConfig config;
    config.performance.configureFor4GB();
    config.auto_detect_platform = false;
    config.platform_override = "4gb";
    return config;
}

RTSPModuleConfig RTSPConfigManager::createFor8GB() {
    RTSPModuleConfig config;
    config.performance.configureFor8GB();
    config.auto_detect_platform = false;
    config.platform_override = "8gb";
    return config;
}

RTSPModuleConfig RTSPConfigManager::createFor16GB() {
    RTSPModuleConfig config;
    config.performance.configureFor16GB();
    config.auto_detect_platform = false;
    config.platform_override = "16gb";
    return config;
}

RTSPModuleConfig RTSPConfigManager::merge(const RTSPModuleConfig& base, const RTSPModuleConfig& override) {
    RTSPModuleConfig result = base;

    // Merge basic module settings
    if (!override.version.empty()) result.version = override.version;
    result.enabled = override.enabled;  // Always use override for boolean

    // Merge platform settings
    if (!override.auto_detect_platform) {
        result.auto_detect_platform = override.auto_detect_platform;
        if (!override.platform_override.empty()) {
            result.platform_override = override.platform_override;
        }
    }

    // Merge performance configuration
    if (override.performance.max_concurrent_streams > 0) {
        result.performance.max_concurrent_streams = override.performance.max_concurrent_streams;
    }
    if (override.performance.thread_pool_size > 0) {
        result.performance.thread_pool_size = override.performance.thread_pool_size;
    }
    if (override.performance.max_memory_usage_mb > 0) {
        result.performance.max_memory_usage_mb = override.performance.max_memory_usage_mb;
    }
    if (override.performance.cpu_usage_limit_percent > 0.0f) {
        result.performance.cpu_usage_limit_percent = override.performance.cpu_usage_limit_percent;
    }

    // Merge hardware acceleration settings
    result.performance.enable_mpp_decoder = override.performance.enable_mpp_decoder;
    result.performance.enable_rga_scaler = override.performance.enable_rga_scaler;
    result.performance.enable_dmabuf_zerocopy = override.performance.enable_dmabuf_zerocopy;
    result.performance.thermal_management = override.performance.thermal_management;
    result.performance.adaptive_quality = override.performance.adaptive_quality;

    // Merge CPU affinity if provided
    if (!override.performance.cpu_affinity.empty()) {
        result.performance.cpu_affinity = override.performance.cpu_affinity;
    }

    // Merge network configuration
    result.network.prefer_tcp = override.network.prefer_tcp;
    result.network.tcp_nodelay = override.network.tcp_nodelay;
    if (override.network.udp_buffer_size > 0) {
        result.network.udp_buffer_size = override.network.udp_buffer_size;
    }
    if (override.network.tcp_buffer_size > 0) {
        result.network.tcp_buffer_size = override.network.tcp_buffer_size;
    }
    if (override.network.keep_alive_interval_ms > 0) {
        result.network.keep_alive_interval_ms = override.network.keep_alive_interval_ms;
    }
    result.network.enable_keep_alive = override.network.enable_keep_alive;
    result.network.ssl_verify_peer = override.network.ssl_verify_peer;
    result.network.ssl_verify_hostname = override.network.ssl_verify_hostname;

    // Merge monitoring configuration
    result.monitoring.enable_statistics = override.monitoring.enable_statistics;
    result.monitoring.enable_performance_monitoring = override.monitoring.enable_performance_monitoring;
    result.monitoring.enable_thermal_monitoring = override.monitoring.enable_thermal_monitoring;
    result.monitoring.enable_memory_monitoring = override.monitoring.enable_memory_monitoring;

    if (override.monitoring.statistics_interval_ms > 0) {
        result.monitoring.statistics_interval_ms = override.monitoring.statistics_interval_ms;
    }
    if (!override.monitoring.log_level.empty()) {
        result.monitoring.log_level = override.monitoring.log_level;
    }
    if (!override.monitoring.log_file_path.empty()) {
        result.monitoring.log_file_path = override.monitoring.log_file_path;
    }

    // Merge streams - override completely replaces base streams
    if (!override.streams.empty()) {
        result.streams = override.streams;
    }

    return result;
}

// Private helper methods
bool RTSPConfigManager::validateStreamConfig(const RTSPConnectionConfig& stream) {
    // Basic validation
    if (!stream.isValid()) return false;

    // Validate timeouts
    if (stream.timeout_ms <= 0 || stream.timeout_ms > 300000) return false;  // Max 5 minutes
    if (stream.connection_timeout_ms <= 0 || stream.connection_timeout_ms > 60000) return false;  // Max 1 minute
    if (stream.read_timeout_ms <= 0 || stream.read_timeout_ms > 30000) return false;  // Max 30 seconds

    // Validate retry settings
    if (stream.retry_count < 0 || stream.retry_count > 10) return false;  // Max 10 retries
    if (stream.retry_delay_ms < 0 || stream.retry_delay_ms > 60000) return false;  // Max 1 minute delay

    // Validate buffer settings
    if (stream.buffer_size_bytes < 64 * 1024 || stream.buffer_size_bytes > 16 * 1024 * 1024) return false;  // 64KB - 16MB
    if (stream.queue_size <= 0 || stream.queue_size > 1000) return false;  // Max 1000 frames in queue
    if (stream.jitter_buffer_size_ms < 0 || stream.jitter_buffer_size_ms > 5000) return false;  // Max 5 seconds

    // Validate video settings
    if (stream.target_resolution.width > 7680 || stream.target_resolution.height > 4320) return false;  // Max 8K
    if (stream.target_framerate.numerator > 120 || stream.target_framerate.denominator == 0) return false;  // Max 120fps
    if (stream.target_bitrate_kbps > 100000) return false;  // Max 100Mbps

    return true;
}

bool RTSPConfigManager::validatePerformanceConfig(const RTSPPerformanceConfig& perf) {
    // Basic validation
    if (perf.max_concurrent_streams <= 0 || perf.max_concurrent_streams > 32) return false;  // Max 32 streams
    if (perf.max_memory_usage_mb <= 0 || perf.max_memory_usage_mb > 16384) return false;  // Max 16GB
    if (perf.thread_pool_size <= 0 || perf.thread_pool_size > 32) return false;  // Max 32 threads
    if (perf.cpu_usage_limit_percent <= 0.0f || perf.cpu_usage_limit_percent > 100.0f) return false;

    // Validate thread configuration
    if (perf.io_thread_count <= 0 || perf.io_thread_count > 16) return false;
    if (perf.worker_thread_count <= 0 || perf.worker_thread_count > 16) return false;

    // Validate CPU affinity (RK3588 has 8 cores: 0-7)
    for (int core : perf.cpu_affinity) {
        if (core < 0 || core > 7) return false;
    }

    // Validate hardware acceleration settings
    if (perf.mpp_decoder_instances <= 0 || perf.mpp_decoder_instances > platform::MAX_MPP_DECODER_INSTANCES) return false;
    if (perf.target_latency_ms <= 0 || perf.target_latency_ms > 5000) return false;  // Max 5 seconds latency

    // Validate thermal settings
    if (perf.thermal_throttle_temperature <= 0 || perf.thermal_throttle_temperature > 100) return false;
    if (perf.thermal_shutdown_temperature <= perf.thermal_throttle_temperature || perf.thermal_shutdown_temperature > 105) return false;

    return true;
}

} // namespace rtsp
} // namespace aibox
